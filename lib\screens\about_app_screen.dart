import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AboutAppScreen extends StatelessWidget {
  const AboutAppScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDarkMode ? const Color(0xFF1A1A1A) : Colors.grey[50],
      appBar: AppBar(
        title: Text(
          'حول التطبيق',
          style: GoogleFonts.cairo(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: isDarkMode ? Colors.white : const Color(0xFF2C3E50),
          ),
        ),
        backgroundColor: isDarkMode ? const Color(0xFF2D2D2D) : Colors.white,
        elevation: 0,
        iconTheme: IconThemeData(
          color: isDarkMode ? Colors.white : const Color(0xFF2C3E50),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // App Logo and Info
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(30),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [const Color(0xFF6366F1), const Color(0xFF8B5CF6)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.purple.withValues(alpha: 0.3),
                    blurRadius: 15,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: Column(
                children: [
                  Container(
                    width: 100,
                    height: 100,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(25),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 10,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: Icon(
                      Icons.school,
                      size: 60,
                      color: const Color(0xFF6366F1),
                    ),
                  ),
                  const SizedBox(height: 20),
                  Text(
                    'Legal 2025',
                    style: GoogleFonts.cairo(
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'منصة طلاب كلية الحقوق',
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      color: Colors.white.withValues(alpha: 0.9),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(15),
                    ),
                    child: Text(
                      'الإصدار 1.0.0',
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 30),

            // Description
            _buildInfoCard(
              context,
              'وصف التطبيق',
              'Legal 2025 هو تطبيق تعليمي مخصص لطلاب كلية الحقوق، يوفر منصة شاملة للوصول إلى المواد الدراسية، التفاعل مع المجتمع الطلابي، ومتابعة آخر الأخبار والإعلانات الأكاديمية.',
              Icons.description,
              Colors.blue,
            ),

            // Features
            _buildInfoCard(
              context,
              'الميزات الرئيسية',
              '''• الوصول للمواد الدراسية والملفات
• مجتمع طلابي تفاعلي
• نظام محادثة متقدم
• تحميل وعرض ملفات PDF
• إشعارات فورية
• واجهة عربية سهلة الاستخدام
• دعم الوضع الليلي''',
              Icons.star,
              Colors.orange,
            ),

            // Developer Info
            _buildInfoCard(
              context,
              'معلومات المطور',
              '''تم تطوير هذا التطبيق بواسطة فريق من طلاب كلية الحقوق بهدف تسهيل العملية التعليمية وتحسين التواصل بين الطلاب.

للتواصل: <EMAIL>''',
              Icons.code,
              Colors.green,
            ),

            // Technology Stack
            _buildInfoCard(
              context,
              'التقنيات المستخدمة',
              '''• Flutter - إطار العمل الرئيسي
• Firebase - قاعدة البيانات والمصادقة
• Google Fonts - الخطوط العربية
• Material Design - تصميم واجهة المستخدم
• Provider - إدارة الحالة''',
              Icons.build,
              Colors.purple,
            ),

            // Contact and Support
            _buildInfoCard(
              context,
              'الدعم والتواصل',
              '''إذا واجهت أي مشاكل أو لديك اقتراحات:
• البريد الإلكتروني: <EMAIL>
• استخدم خاصية "اتصل بنا" في التطبيق
• تقييم التطبيق في متجر التطبيقات''',
              Icons.support_agent,
              Colors.teal,
            ),

            const SizedBox(height: 20),

            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: _buildActionButton(
                    context,
                    'تقييم التطبيق',
                    Icons.star_rate,
                    Colors.amber,
                    () => _rateApp(),
                  ),
                ),
                const SizedBox(width: 15),
                Expanded(
                  child: _buildActionButton(
                    context,
                    'مشاركة التطبيق',
                    Icons.share,
                    Colors.blue,
                    () => _shareApp(),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // Footer
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: isDarkMode ? const Color(0xFF2D2D2D) : Colors.grey[100],
                borderRadius: BorderRadius.circular(15),
                border: Border.all(
                  color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
                ),
              ),
              child: Column(
                children: [
                  Icon(Icons.favorite, size: 30, color: Colors.red[400]),
                  const SizedBox(height: 10),
                  Text(
                    'صُنع بحب لطلاب كلية الحقوق',
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color:
                          isDarkMode ? Colors.white : const Color(0xFF2C3E50),
                    ),
                  ),
                  const SizedBox(height: 5),
                  Text(
                    'جامعة القاهرة - ${DateTime.now().year}',
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoCard(
    BuildContext context,
    String title,
    String content,
    IconData icon,
    Color color,
  ) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF2D2D2D) : Colors.white,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color:
                isDarkMode
                    ? Colors.black26
                    : Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(icon, color: color, size: 24),
              ),
              const SizedBox(width: 15),
              Expanded(
                child: Text(
                  title,
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? Colors.white : const Color(0xFF2C3E50),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 15),
          Text(
            content,
            style: GoogleFonts.cairo(
              fontSize: 15,
              height: 1.6,
              color: isDarkMode ? Colors.grey[300] : Colors.grey[700],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 15),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [color, color.withValues(alpha: 0.8)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: color.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Text(
              title,
              style: GoogleFonts.cairo(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _rateApp() {
    // يمكن إضافة رابط متجر التطبيقات هنا
    // TODO: إضافة رابط متجر التطبيقات
  }

  void _shareApp() {
    // يمكن إضافة مشاركة التطبيق هنا
    // TODO: إضافة مشاركة التطبيق
  }
}
