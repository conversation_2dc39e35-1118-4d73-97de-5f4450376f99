import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../services/enhanced_download_service.dart';
import '../widgets/download_progress_dialog.dart';

/// بطاقة PDF مبسطة مع زر تحميل حقيقي
class SimplePDFCard extends StatefulWidget {
  final String title;
  final String url;
  final String fileName;
  final String? subtitle;
  final VoidCallback? onTap;
  final VoidCallback? onDownloadSuccess;

  const SimplePDFCard({
    super.key,
    required this.title,
    required this.url,
    required this.fileName,
    this.subtitle,
    this.onTap,
    this.onDownloadSuccess,
  });

  @override
  State<SimplePDFCard> createState() => _SimplePDFCardState();
}

class _SimplePDFCardState extends State<SimplePDFCard> {
  bool _isDownloading = false;

  /// تحميل PDF فعلي
  Future<void> _downloadPdf() async {
    if (_isDownloading) return;

    setState(() {
      _isDownloading = true;
    });

    try {
      // عرض dialog التحميل
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => DownloadProgressDialog(
            url: widget.url,
            fileName: widget.fileName,
            onSuccess: () {
              if (mounted) {
                Navigator.of(context).pop();
                widget.onDownloadSuccess?.call();
                
                // عرض رسالة نجاح
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'تم تحميل ${widget.fileName} بنجاح',
                      style: GoogleFonts.cairo(),
                    ),
                    backgroundColor: Colors.green,
                    duration: const Duration(seconds: 3),
                  ),
                );
              }
            },
            onError: (error) {
              if (mounted) {
                Navigator.of(context).pop();
                
                // عرض رسالة خطأ
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'فشل في التحميل: $error',
                      style: GoogleFonts.cairo(),
                    ),
                    backgroundColor: Colors.red,
                    duration: const Duration(seconds: 3),
                  ),
                );
              }
            },
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'خطأ في التحميل: $e',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isDownloading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.red.shade50,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.picture_as_pdf,
            color: Colors.red.shade600,
            size: 28,
          ),
        ),
        title: Text(
          widget.title,
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        subtitle: widget.subtitle != null
            ? Text(
                widget.subtitle!,
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              )
            : null,
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // زر العرض
            IconButton(
              onPressed: widget.onTap,
              icon: Icon(
                Icons.visibility,
                color: Colors.blue.shade600,
              ),
              tooltip: 'عرض',
            ),
            // زر التحميل الحقيقي
            IconButton(
              onPressed: _isDownloading ? null : _downloadPdf,
              icon: _isDownloading
                  ? SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Colors.green.shade600,
                        ),
                      ),
                    )
                  : Icon(
                      Icons.download,
                      color: Colors.green.shade600,
                    ),
              tooltip: _isDownloading ? 'جاري التحميل...' : 'تحميل',
            ),
          ],
        ),
        onTap: widget.onTap,
      ),
    );
  }
}
