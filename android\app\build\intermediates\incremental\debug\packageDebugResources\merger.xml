<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\20223\2025\legl92025\android\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\20223\2025\legl92025\android\app\src\main\res"><file name="launch_background" path="D:\20223\2025\legl92025\android\app\src\main\res\drawable\launch_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="D:\20223\2025\legl92025\android\app\src\main\res\drawable-hdpi\ic_launcher_foreground.png" qualifiers="hdpi-v4" type="drawable"/><file name="ic_launcher_foreground" path="D:\20223\2025\legl92025\android\app\src\main\res\drawable-mdpi\ic_launcher_foreground.png" qualifiers="mdpi-v4" type="drawable"/><file name="launch_background" path="D:\20223\2025\legl92025\android\app\src\main\res\drawable-v21\launch_background.xml" qualifiers="v21" type="drawable"/><file name="ic_launcher_foreground" path="D:\20223\2025\legl92025\android\app\src\main\res\drawable-xhdpi\ic_launcher_foreground.png" qualifiers="xhdpi-v4" type="drawable"/><file name="ic_launcher_foreground" path="D:\20223\2025\legl92025\android\app\src\main\res\drawable-xxhdpi\ic_launcher_foreground.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="ic_launcher_foreground" path="D:\20223\2025\legl92025\android\app\src\main\res\drawable-xxxhdpi\ic_launcher_foreground.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="ic_launcher" path="D:\20223\2025\legl92025\android\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="D:\20223\2025\legl92025\android\app\src\main\res\mipmap-hdpi\ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\20223\2025\legl92025\android\app\src\main\res\mipmap-mdpi\ic_launcher.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\20223\2025\legl92025\android\app\src\main\res\mipmap-xhdpi\ic_launcher.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\20223\2025\legl92025\android\app\src\main\res\mipmap-xxhdpi\ic_launcher.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\20223\2025\legl92025\android\app\src\main\res\mipmap-xxxhdpi\ic_launcher.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="D:\20223\2025\legl92025\android\app\src\main\res\values\colors.xml" qualifiers=""><color name="ic_launcher_background">#1E3A8A</color></file><file path="D:\20223\2025\legl92025\android\app\src\main\res\values\styles.xml" qualifiers=""><style name="LaunchTheme" parent="@android:style/Theme.Light.NoTitleBar">
        
        <item name="android:windowBackground">@drawable/launch_background</item>
    </style><style name="NormalTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style></file><file path="D:\20223\2025\legl92025\android\app\src\main\res\values-night\styles.xml" qualifiers="night-v8"><style name="LaunchTheme" parent="@android:style/Theme.Black.NoTitleBar">
        
        <item name="android:windowBackground">@drawable/launch_background</item>
    </style><style name="NormalTheme" parent="@android:style/Theme.Black.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style></file><file name="network_security_config" path="D:\20223\2025\legl92025\android\app\src\main\res\xml\network_security_config.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\20223\2025\legl92025\android\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\20223\2025\legl92025\android\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\20223\2025\legl92025\android\app\build\generated\res\resValues\debug"/><source path="D:\20223\2025\legl92025\android\app\build\generated\res\processDebugGoogleServices"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\20223\2025\legl92025\android\app\build\generated\res\resValues\debug"/><source path="D:\20223\2025\legl92025\android\app\build\generated\res\processDebugGoogleServices"><file path="D:\20223\2025\legl92025\android\app\build\generated\res\processDebugGoogleServices\values\values.xml" qualifiers=""><string name="default_web_client_id" translatable="false">801031214670-qhqjqhqjqhqjqhqjqhqjqhqjqhqjqhqj.apps.googleusercontent.com</string><string name="gcm_defaultSenderId" translatable="false">801031214670</string><string name="google_api_key" translatable="false">AIzaSyD__fwZYKuehthlJoqGKSDt0YN1TCsPUY8</string><string name="google_app_id" translatable="false">1:801031214670:android:a179401f6b476d34db551f</string><string name="google_crash_reporting_api_key" translatable="false">AIzaSyD__fwZYKuehthlJoqGKSDt0YN1TCsPUY8</string><string name="google_storage_bucket" translatable="false">legal2025.firebasestorage.app</string><string name="project_id" translatable="false">legal2025</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices" generated-set="res-processDebugGoogleServices$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>