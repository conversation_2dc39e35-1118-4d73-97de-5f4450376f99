import 'dart:io';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../services/download_service.dart';
import '../services/permission_manager.dart';
import '../services/local_download_service.dart';
import '../widgets/advanced_pdf_viewer.dart';
import '../widgets/permission_request_widget.dart';

class EnhancedDownloadsScreen extends StatefulWidget {
  const EnhancedDownloadsScreen({super.key});

  @override
  State<EnhancedDownloadsScreen> createState() =>
      _EnhancedDownloadsScreenState();
}

class _EnhancedDownloadsScreenState extends State<EnhancedDownloadsScreen> {
  List<DownloadedFile> _downloadedFiles = [];
  List<LocalDownloadInfo> _localDownloads = [];
  bool _isLoading = true;
  bool _hasPermissions = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _initializeScreen();
  }

  Future<void> _initializeScreen() async {
    await _checkPermissions();
    if (_hasPermissions) {
      await _loadDownloadedFiles();
    }
  }

  Future<void> _checkPermissions() async {
    bool hasPermissions = await PermissionManager.checkPermissions();
    setState(() {
      _hasPermissions = hasPermissions;
    });
  }

  Future<void> _loadDownloadedFiles() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // تحميل الملفات المحلية الجديدة
      final localDownloads = await LocalDownloadService.getLocalDownloads();

      // تحميل الملفات القديمة
      Directory downloadDir = await DownloadService.getDownloadDirectory();
      debugPrint('البحث في مجلد: ${downloadDir.path}');

      if (!await downloadDir.exists()) {
        setState(() {
          _downloadedFiles = [];
          _isLoading = false;
        });
        return;
      }

      List<FileSystemEntity> files = await downloadDir.list().toList();
      List<DownloadedFile> downloadedFiles = [];

      for (FileSystemEntity entity in files) {
        if (entity is File && entity.path.toLowerCase().endsWith('.pdf')) {
          try {
            FileStat stat = await entity.stat();
            String fileName = entity.path.split('/').last;
            int fileSize = await entity.length();

            // التحقق من صحة الملف
            if (fileSize > 0) {
              downloadedFiles.add(
                DownloadedFile(
                  name: fileName,
                  path: entity.path,
                  size: fileSize,
                  downloadDate: stat.modified,
                ),
              );
            }
          } catch (e) {
            debugPrint('خطأ في قراءة معلومات الملف ${entity.path}: $e');
          }
        }
      }

      // دمج الملفات المحلية مع الملفات القديمة
      for (final localDownload in localDownloads) {
        downloadedFiles.add(
          DownloadedFile(
            name: localDownload.fileName,
            path: localDownload.filePath,
            size: localDownload.fileSize,
            downloadDate: localDownload.downloadDate,
            isLocalDownload: true, // تمييز الملفات المحلية
          ),
        );
      }

      // ترتيب الملفات حسب تاريخ التحميل (الأحدث أولاً)
      downloadedFiles.sort((a, b) => b.downloadDate.compareTo(a.downloadDate));

      setState(() {
        _downloadedFiles = downloadedFiles;
        _localDownloads = localDownloads;
        _isLoading = false;
      });

      debugPrint(
        'تم العثور على ${downloadedFiles.length} ملف PDF (${localDownloads.length} محلي)',
      );
    } catch (e) {
      debugPrint('خطأ في تحميل الملفات المحملة: $e');
      setState(() {
        _errorMessage = 'خطأ في تحميل الملفات: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _openFile(DownloadedFile file) async {
    try {
      // التحقق من وجود الملف
      bool isValid = await DownloadService.isValidFilePath(file.path);
      if (!isValid) {
        _showErrorDialog('الملف غير موجود أو تالف: ${file.name}');
        return;
      }

      // فتح الملف في العارض المحسن
      if (mounted) {
        if (file.isLocalDownload) {
          // للملفات المحلية - فتح مباشر من المسار
          Navigator.push(
            context,
            MaterialPageRoute(
              builder:
                  (context) => AdvancedPDFViewer(
                    pdfUrl: 'file://${file.path}', // مسار محلي
                    fileName: file.name,
                    title: file.name,
                  ),
            ),
          );
        } else {
          // للملفات القديمة - فتح عادي
          Navigator.push(
            context,
            MaterialPageRoute(
              builder:
                  (context) => AdvancedPDFViewer(
                    pdfUrl: file.path,
                    fileName: file.name,
                    title: file.name,
                  ),
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('خطأ في فتح الملف: $e');
      _showErrorDialog('خطأ في فتح الملف: $e');
    }
  }

  Future<void> _deleteFile(DownloadedFile file) async {
    bool? confirm = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تأكيد الحذف'),
            content: Text('هل تريد حذف الملف "${file.name}"؟'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                child: const Text('حذف'),
              ),
            ],
          ),
    );

    if (confirm == true) {
      try {
        File fileToDelete = File(file.path);
        if (await fileToDelete.exists()) {
          await fileToDelete.delete();
          await _loadDownloadedFiles(); // إعادة تحميل القائمة

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('تم حذف الملف "${file.name}"')),
            );
          }
        }
      } catch (e) {
        debugPrint('خطأ في حذف الملف: $e');
        _showErrorDialog('خطأ في حذف الملف: $e');
      }
    }
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('خطأ'),
            content: Text(message),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('موافق'),
              ),
            ],
          ),
    );
  }

  Widget _buildFileItem(DownloadedFile file) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        leading: Stack(
          children: [
            const Icon(Icons.picture_as_pdf, color: Colors.red, size: 40),
            if (file.isLocalDownload)
              Positioned(
                right: 0,
                top: 0,
                child: Container(
                  padding: const EdgeInsets.all(2),
                  decoration: const BoxDecoration(
                    color: Colors.green,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.download_done,
                    color: Colors.white,
                    size: 12,
                  ),
                ),
              ),
          ],
        ),
        title: Text(
          file.name,
          style: const TextStyle(fontWeight: FontWeight.bold),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('الحجم: ${DownloadService.formatBytes(file.size)}'),
            Text('التاريخ: ${_formatDate(file.downloadDate)}'),
            if (file.isLocalDownload)
              Text(
                'محفوظ محلياً',
                style: GoogleFonts.cairo(
                  color: Colors.green,
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'open':
                _openFile(file);
                break;
              case 'delete':
                _deleteFile(file);
                break;
            }
          },
          itemBuilder:
              (context) => [
                const PopupMenuItem(
                  value: 'open',
                  child: Row(
                    children: [
                      Icon(Icons.open_in_new),
                      SizedBox(width: 8),
                      Text('فتح'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: Row(
                    children: [
                      Icon(Icons.delete, color: Colors.red),
                      SizedBox(width: 8),
                      Text('حذف'),
                    ],
                  ),
                ),
              ],
        ),
        onTap: () => _openFile(file),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الملفات المحملة'),
        actions: [
          IconButton(
            onPressed: _hasPermissions ? _loadDownloadedFiles : null,
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث',
          ),
        ],
      ),
      body:
          !_hasPermissions
              ? FilePermissionWidget(
                onPermissionGranted: () {
                  setState(() {
                    _hasPermissions = true;
                  });
                  _loadDownloadedFiles();
                },
              )
              : _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _errorMessage != null
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.error_outline,
                      size: 64,
                      color: Theme.of(context).colorScheme.error,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      _errorMessage!,
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.bodyLarge,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton.icon(
                      onPressed: _loadDownloadedFiles,
                      icon: const Icon(Icons.refresh),
                      label: const Text('إعادة المحاولة'),
                    ),
                  ],
                ),
              )
              : _downloadedFiles.isEmpty
              ? const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.folder_open, size: 64, color: Colors.grey),
                    SizedBox(height: 16),
                    Text(
                      'لا توجد ملفات محملة',
                      style: TextStyle(fontSize: 18, color: Colors.grey),
                    ),
                  ],
                ),
              )
              : ListView.builder(
                itemCount: _downloadedFiles.length,
                itemBuilder: (context, index) {
                  return _buildFileItem(_downloadedFiles[index]);
                },
              ),
    );
  }
}

class DownloadedFile {
  final String name;
  final String path;
  final int size;
  final DateTime downloadDate;
  final bool isLocalDownload;

  DownloadedFile({
    required this.name,
    required this.path,
    required this.size,
    required this.downloadDate,
    this.isLocalDownload = false,
  });
}
