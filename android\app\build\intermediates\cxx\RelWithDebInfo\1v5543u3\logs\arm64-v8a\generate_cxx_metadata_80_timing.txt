# C/C++ build system timings
generate_cxx_metadata
  [gap of 30ms]
  create-invalidation-state 127ms
  [gap of 44ms]
  write-metadata-json-to-file 19ms
generate_cxx_metadata completed in 222ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 21ms]
  create-invalidation-state 57ms
  [gap of 33ms]
  write-metadata-json-to-file 18ms
generate_cxx_metadata completed in 130ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 48ms]
  create-invalidation-state 144ms
  [gap of 62ms]
  write-metadata-json-to-file 30ms
generate_cxx_metadata completed in 286ms

