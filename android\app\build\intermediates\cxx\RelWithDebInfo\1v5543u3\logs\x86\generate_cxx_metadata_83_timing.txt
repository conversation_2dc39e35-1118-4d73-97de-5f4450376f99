# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 115ms
  [gap of 444ms]
  write-metadata-json-to-file 33ms
generate_cxx_metadata completed in 598ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 20ms
  [gap of 35ms]
  write-metadata-json-to-file 11ms
generate_cxx_metadata completed in 73ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 11ms
  [gap of 19ms]
generate_cxx_metadata completed in 33ms

# C/C++ build system timings
generate_cxx_metadata 16ms

