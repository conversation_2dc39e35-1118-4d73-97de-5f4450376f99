# C/C++ build system timings
generate_cxx_metadata
  [gap of 123ms]
  create-invalidation-state 384ms
  [gap of 296ms]
  write-metadata-json-to-file 127ms
generate_cxx_metadata completed in 935ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 96ms]
  create-invalidation-state 153ms
  [gap of 84ms]
  write-metadata-json-to-file 30ms
generate_cxx_metadata completed in 365ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 21ms]
  create-invalidation-state 64ms
  [gap of 48ms]
generate_cxx_metadata completed in 133ms

