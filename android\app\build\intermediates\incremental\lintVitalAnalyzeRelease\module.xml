<lint-module
    format="1"
    dir="D:\20223\2025\legl92025\android\app"
    name=":app"
    type="APP"
    maven="android:app:unspecified"
    agpVersion="8.7.0"
    buildFolder="build"
    bootClassPath="C:\Users\<USER>\AppData\Local\Android\sdk\platforms\android-35\android.jar;C:\Users\<USER>\AppData\Local\Android\sdk\build-tools\34.0.0\core-lambda-stubs.jar"
    javaSourceLevel="17"
    compileTarget="android-35"
    neverShrinking="true">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="release"/>
</lint-module>
