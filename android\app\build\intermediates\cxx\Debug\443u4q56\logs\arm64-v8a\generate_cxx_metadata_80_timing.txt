# C/C++ build system timings
generate_cxx_metadata
  [gap of 92ms]
  create-invalidation-state 451ms
  [gap of 185ms]
  write-metadata-json-to-file 153ms
generate_cxx_metadata completed in 884ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 30ms]
  create-invalidation-state 109ms
  [gap of 43ms]
  write-metadata-json-to-file 19ms
generate_cxx_metadata completed in 203ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 25ms]
  create-invalidation-state 84ms
  [gap of 52ms]
  write-metadata-json-to-file 16ms
generate_cxx_metadata completed in 180ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 335ms]
  create-invalidation-state 506ms
  [gap of 262ms]
  write-metadata-json-to-file 98ms
generate_cxx_metadata completed in 1205ms

